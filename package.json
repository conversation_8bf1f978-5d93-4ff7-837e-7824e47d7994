{"name": "hztech-front-end", "version": "1.0.0", "scripts": {"dev": "vite --host", "prod": "vite --mode production", "build": "vite build", "build:prod": "vite build --mode production", "serve": "vite preview --host"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons-vue": "^6.0.1", "@cesium-china/cesium-map": "^1.0.0", "@element-plus/icons-vue": "^2.0.9", "@liveqing/liveplayer": "^2.7.10", "@smallwei/avue": "3.4.3", "@vitejs/plugin-legacy": "^1.6.2", "@vuemap/vue-amap": "^2.1.5", "agora-rtc-sdk-ng": "^4.12.1", "animate.css": "^4.1.1", "ant-design-vue": "2.2.8", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "cesium": "^1.128.0", "coordtransform": "^2.1.2", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "echarts": "^5.5.1", "element-plus": "^2.7.3", "eventemitter3": "^5.0.0", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "jszip": "^3.10.1", "mitt": "^3.0.0", "mockjs": "^1.1.0", "moment": "^2.30.1", "mqtt": "^4.3.7", "nprogress": "^0.2.0", "ol": "^6.14.1", "p-limit": "^6.1.0", "path": "^0.12.7", "proj4js": "^10.0.0", "qs": "^6.13.0", "reconnecting-websocket": "^4.4.0", "sm-crypto": "^0.3.13", "spark-md5": "^3.0.2", "uuid": "^8.3.2", "vconsole": "^3.15.1", "vite-plugin-importer": "0.2.5", "vite-plugin-mock": "^2.9.4", "vite-plugin-optimize-persist": "0.1.2", "vite-plugin-package-config": "0.1.1", "vite-plugin-svg-icons": "1.0.5", "vue": "^3.4.27", "vue-amap": "^0.5.10", "vue-cookies": "^1.7.4", "vue-i18n": "^9.1.9", "vue-router": "4.0", "vuex": "^4.0.2"}, "devDependencies": {"@types/spark-md5": "^3.0.5", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.27", "prettier": "^2.8.7", "sass": "1.32.13", "unplugin-auto-import": "^0.11.2", "vite": "^5.2.12", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}, "main": "index.js", "repository": "https://code.dstyun.com/hztech-flight/hztech-flight-pc.git", "author": "chen<PERSON><PERSON>n", "license": "MIT"}